/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #000000;
  background-color: #FFFFFF;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: 0.5rem;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

p {
  margin-bottom: 1rem;
}

/* Links */
a {
  color: #E6B120;
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: #FFCD29;
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #E6B120;
  color: #FFFFFF;
  border-color: #E6B120;
}

.btn-primary:hover:not(:disabled) {
  background-color: #FFCD29;
  border-color: #FFCD29;
  text-decoration: none;
  color: #000000;
}

.btn-secondary {
  background-color: #000000;
  color: #FFFFFF;
  border-color: #000000;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #333333;
  border-color: #333333;
  text-decoration: none;
  color: #FFFFFF;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
  text-decoration: none;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #000000;
  border-color: #E6B120;
}

.btn-outline:hover:not(:disabled) {
  background-color: #FFCD29;
  text-decoration: none;
  color: #000000;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #000000;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #E6B120;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #000000;
  background-color: #FFFFFF;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #FFCD29;
  box-shadow: 0 0 0 3px rgba(255, 205, 41, 0.2);
}

.form-control:disabled {
  background-color: #f9fafb;
  color: #6b7280;
}

.form-control.is-invalid {
  border-color: #ef4444;
}

.form-control.is-invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #ef4444;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }

/* Additional utility classes */
.text-muted {
  color: #6c757d !important;
}

.text-danger {
  color: #dc3545 !important;
}

.font-weight-500 {
  font-weight: 500 !important;
}

.text-sm {
  font-size: 0.875rem !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

/* Import component styles */
@import './components/image-upload.css';
@import './components/image-cropper.css';
@import './components/searchable-dropdown.css';
@import './components/price-formatter.css';
@import './components/qr-generator.css';