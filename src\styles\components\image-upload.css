/* Image Upload Component Styles */
.image-upload-container {
  width: 100%;
}

.image-upload-area {
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 200px;
}

/* Multiple images variant */
.image-upload-container.multiple-images .image-upload-area {
  min-height: 250px;
}

.image-upload-area:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.image-upload-area.drag-over {
  border-color: #007bff;
  background: #e3f2fd;
  transform: scale(1.02);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  height: 100%;
  min-height: 200px;
}

.upload-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

.upload-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin: 0 0 0.5rem 0;
}

.upload-subtitle {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0 0 0.5rem 0;
}

.upload-info {
  font-size: 0.8rem;
  color: #868e96;
  margin: 0;
}

.upload-stats {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
}

.files-count {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  background: #e9ecef;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  display: inline-block;
}

/* Image Previews */
.image-previews {
  padding: 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.image-preview {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.image-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.preview-image {
  position: relative;
  width: 100%;
  aspect-ratio: 1 / 1; /* Force 1:1 square aspect ratio */
  overflow: hidden;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.remove-image:hover {
  background: rgba(220, 53, 69, 1);
}

.image-index {
  position: absolute;
  top: 5px;
  left: 5px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.preview-info {
  padding: 0.75rem;
}

.preview-name {
  font-size: 0.85rem;
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-size {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Upload Actions */
.upload-actions {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  margin-top: 1rem;
}

.upload-actions .btn {
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.upload-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-actions .add-more-btn {
  background: #fff;
  border: 1px solid #007bff;
  color: #007bff;
}

.upload-actions .add-more-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.upload-actions .clear-all-btn {
  background: #fff;
  border: 1px solid #dc3545;
  color: #dc3545;
}

.upload-actions .clear-all-btn:hover {
  background: #dc3545;
  color: white;
}

/* Progress Indicator */
.upload-progress {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  font-size: 0.85rem;
  color: #495057;
  text-align: center;
}

/* Single Image Upload Variant */
.image-upload-container.single-image .image-previews {
  grid-template-columns: 1fr;
  max-width: 300px;
  margin: 0 auto;
}

.image-upload-container.single-image .image-preview {
  max-width: 100%;
}

.image-upload-container.single-image .preview-image {
  aspect-ratio: 1 / 1; /* Maintain 1:1 aspect ratio for single images */
}

/* Responsive Design */
@media (max-width: 768px) {
  .image-previews {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    padding: 0.75rem;
  }
  
  .preview-image {
    aspect-ratio: 1 / 1; /* Maintain 1:1 aspect ratio on mobile */
  }
  
  .upload-placeholder {
    padding: 1.5rem;
    min-height: 150px;
  }
  
  .upload-icon {
    font-size: 2.5rem;
  }
  
  .upload-title {
    font-size: 1rem;
  }
}

/* Error States */
.image-upload-area.error {
  border-color: #dc3545;
  background: #f8d7da;
}

.image-upload-area.error .upload-icon {
  color: #dc3545;
}

/* Loading State */
.image-upload-area.loading {
  pointer-events: none;
  opacity: 0.7;
}

.image-upload-area.loading .upload-placeholder::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
